import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import '../../domain/entities/customer.dart';
import '../../domain/entities/customer_account_statement_item.dart';
import '../providers/customer_provider.dart';
import '../../../../shared_widgets/wrappers.dart';

class CustomerAccountStatementDetailsScreen extends StatefulWidget {
  final int customerId;

  const CustomerAccountStatementDetailsScreen({
    super.key,
    required this.customerId,
  });

  @override
  State<CustomerAccountStatementDetailsScreen> createState() =>
      _CustomerAccountStatementDetailsScreenState();
}

class _CustomerAccountStatementDetailsScreenState
    extends State<CustomerAccountStatementDetailsScreen> {
  Customer? _customer;
  bool _isLoadingCustomer = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadData();
    });
  }

  Future<void> _loadData() async {
    final provider = context.read<CustomerProvider>();

    // تحميل بيانات العميل
    setState(() => _isLoadingCustomer = true);
    try {
      _customer = await provider.getCustomerById(widget.customerId);
    } catch (e) {
      // Handle error
    } finally {
      if (mounted) {
        setState(() => _isLoadingCustomer = false);
      }
    }

    // تحميل كشف الحساب
    await provider.fetchAccountStatement(widget.customerId);
  }

  @override
  Widget build(BuildContext context) {
    return SecondaryScreenWrapper(
      title: _customer?.name ?? 'كشف حساب العميل',
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          context.push(
            '/transactions/receipts/new',
            extra: {
              'relatedEntityType': 'customer',
              'relatedEntityId': widget.customerId,
            },
          );
        },
        backgroundColor: Colors.green,
        child: const Icon(Icons.add, color: Colors.white),
      ),
      child: Consumer<CustomerProvider>(
        builder: (context, provider, child) {
          if (_isLoadingCustomer || provider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (provider.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: Colors.red.shade400,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    provider.errorMessage!,
                    style: const TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: _loadData,
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            );
          }

          if (provider.statementItems.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.receipt_long_outlined,
                    size: 64,
                    color: Colors.grey.shade400,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد معاملات لهذا العميل حتى الآن',
                    style: TextStyle(fontSize: 16),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return Column(
            children: [
              // رأس الكشف (Header)
              _buildAccountSummaryCard(provider.statementItems),
              const SizedBox(height: 16),

              // رأس الجدول
              _buildTableHeader(),
              const SizedBox(height: 8),

              // قائمة المعاملات
              Expanded(child: _buildTransactionsList(provider.statementItems)),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAccountSummaryCard(List<CustomerAccountStatementItem> items) {
    // حساب الإجماليات
    double totalDebits = 0.0;
    double totalCredits = 0.0;
    double finalBalance = 0.0;

    for (final item in items) {
      totalDebits += item.debit;
      totalCredits += item.credit;
    }

    if (items.isNotEmpty) {
      finalBalance = items.first.runningBalance; // الأحدث أولاً
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // اسم العميل
            Text(
              _customer?.name ?? 'عميل غير معروف',
              style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // الملخص المالي
            Row(
              children: [
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الديون',
                    totalDebits,
                    Colors.blue,
                    Icons.trending_up,
                  ),
                ),
                Expanded(
                  child: _buildSummaryItem(
                    'إجمالي الدفعات',
                    totalCredits,
                    Colors.green,
                    Icons.trending_down,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // الرصيد النهائي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: finalBalance > 0
                    ? Colors.red.shade50
                    : finalBalance < 0
                    ? Colors.green.shade50
                    : Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: finalBalance > 0
                      ? Colors.red.shade200
                      : finalBalance < 0
                      ? Colors.green.shade200
                      : Colors.grey.shade200,
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'الرصيد النهائي:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  Text(
                    '${finalBalance.toStringAsFixed(2)} ر.ي',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: finalBalance > 0
                          ? Colors.red.shade700
                          : finalBalance < 0
                          ? Colors.green.shade700
                          : Colors.grey.shade700,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    String title,
    double amount,
    Color color,
    IconData icon,
  ) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            '${amount.toStringAsFixed(2)} ر.ي',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: const Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              'التاريخ',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              'البيان',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'مدين',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'دائن',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              'الرصيد',
              style: TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionsList(List<CustomerAccountStatementItem> items) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return _buildTransactionCard(item);
      },
    );
  }

  Widget _buildTransactionCard(CustomerAccountStatementItem item) {
    final dateFormat = DateFormat('dd/MM/yyyy');

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
        child: Row(
          children: [
            // التاريخ
            Expanded(
              flex: 2,
              child: Text(
                dateFormat.format(item.transaction.transactionDate),
                style: const TextStyle(fontSize: 12),
              ),
            ),

            // البيان
            Expanded(
              flex: 3,
              child: Text(
                item.transaction.description ?? 'بدون وصف',
                style: const TextStyle(fontSize: 12),
                overflow: TextOverflow.ellipsis,
              ),
            ),

            // مدين
            Expanded(
              flex: 2,
              child: Text(
                item.debit > 0 ? item.debit.toStringAsFixed(2) : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.blue.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // دائن
            Expanded(
              flex: 2,
              child: Text(
                item.credit > 0 ? item.credit.toStringAsFixed(2) : '',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.green.shade700,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ),

            // الرصيد
            Expanded(
              flex: 2,
              child: Text(
                item.runningBalance.toStringAsFixed(2),
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: item.runningBalance > 0
                      ? Colors.red.shade700
                      : item.runningBalance < 0
                      ? Colors.green.shade700
                      : Colors.grey.shade700,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
