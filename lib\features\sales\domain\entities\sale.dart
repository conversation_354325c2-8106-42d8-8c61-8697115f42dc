import '../../data/models/sale_model.dart';

class Sale {
  final int? id;
  final int? customerId;
  final DateTime saleDate;
  final double totalAmount;
  final double totalPaidAmount;
  final String paymentMethod;
  final String? notes;
  final String status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const Sale({
    this.id,
    this.customerId,
    required this.saleDate,
    required this.totalAmount,
    this.totalPaidAmount = 0.0,
    required this.paymentMethod,
    this.notes,
    required this.status,
    this.createdAt,
    this.updatedAt,
  });

  // Create Sale from SaleModel
  factory Sale.fromModel(SaleModel model) {
    return Sale(
      id: model.id,
      customerId: model.customerId,
      saleDate: model.saleDate,
      totalAmount: model.totalAmount,
      totalPaidAmount: model.totalPaidAmount,
      paymentMethod: model.paymentMethod,
      notes: model.notes,
      status: model.status,
    );
  }

  // Computed properties
  double get dueAmount => totalAmount - totalPaidAmount;

  // Business logic methods
  bool get isFullyPaid => dueAmount <= 0;
  bool get isPartiallyPaid => totalPaidAmount > 0 && dueAmount > 0;
  bool get isUnpaid => totalPaidAmount <= 0;
  bool get isCash => paymentMethod == 'cash';
  bool get isCredit => paymentMethod == 'credit';
  bool get isBankTransfer => paymentMethod == 'bank_transfer';

  String get paymentMethodDisplayName {
    switch (paymentMethod) {
      case 'cash':
        return 'نقدي';
      case 'credit':
        return 'آجل';
      case 'bank_transfer':
        return 'تحويل بنكي';
      default:
        return paymentMethod;
    }
  }

  String get statusDisplayName {
    switch (status) {
      case 'completed':
        return 'مكتملة';
      case 'pending':
        return 'قيد الانتظار';
      case 'pending_payment':
        return 'في انتظار الدفع';
      case 'cancelled':
        return 'ملغية';
      default:
        return status;
    }
  }

  String get paymentStatusText {
    if (isFullyPaid) return 'مدفوعة بالكامل';
    if (isPartiallyPaid) return 'مدفوعة جزئياً';
    return 'غير مدفوعة';
  }

  String get paymentStatus {
    if (isFullyPaid) return 'paid';
    if (isPartiallyPaid) return 'partial';
    return 'unpaid';
  }

  // Copy with method for updates
  Sale copyWith({
    int? id,
    int? customerId,
    DateTime? saleDate,
    double? totalAmount,
    double? totalPaidAmount,
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return Sale(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      saleDate: saleDate ?? this.saleDate,
      totalAmount: totalAmount ?? this.totalAmount,
      totalPaidAmount: totalPaidAmount ?? this.totalPaidAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'Sale(id: $id, customerId: $customerId, saleDate: $saleDate, '
        'totalAmount: $totalAmount, totalPaidAmount: $totalPaidAmount, dueAmount: $dueAmount, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Sale &&
        other.id == id &&
        other.customerId == customerId &&
        other.saleDate == saleDate &&
        other.totalAmount == totalAmount &&
        other.totalPaidAmount == totalPaidAmount &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        saleDate.hashCode ^
        totalAmount.hashCode ^
        totalPaidAmount.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
