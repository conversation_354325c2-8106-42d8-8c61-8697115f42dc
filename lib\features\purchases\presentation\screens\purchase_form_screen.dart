import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';
import 'package:market/shared_widgets/wrappers.dart';
import 'package:market/features/suppliers/presentation/providers/supplier_provider.dart';
import 'package:market/features/suppliers/domain/entities/supplier.dart';
import 'package:market/features/products/presentation/providers/product_provider.dart';
import 'package:market/features/products/domain/entities/product.dart';
import 'package:market/features/products/domain/usecases/get_product_by_id.dart';
import 'package:market/features/purchases/presentation/providers/purchase_provider.dart';
import 'package:market/features/purchases/domain/entities/purchase.dart';
import 'package:market/features/purchases/domain/entities/purchase_item.dart';

// Helper class for UI display
class PurchaseItemDisplay {
  final int? productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final String? unit; // إضافة الوحدة

  PurchaseItemDisplay({
    this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    this.unit,
  });

  double get totalPrice => quantity * unitPrice;
}

class PurchaseFormScreen extends StatefulWidget {
  final int? purchaseId; // إضافة معامل purchaseId للتعديل

  const PurchaseFormScreen({super.key, this.purchaseId});

  @override
  State<PurchaseFormScreen> createState() => _PurchaseFormScreenState();
}

class _PurchaseFormScreenState extends State<PurchaseFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _invoiceNumberController = TextEditingController();
  final _notesController = TextEditingController();
  final _discountController = TextEditingController(); // حقل الخصم
  final _paidAmountController = TextEditingController(); // حقل المبلغ المدفوع
  final _paidAmountFocusNode = FocusNode(); // FocusNode للمبلغ المدفوع

  Supplier? _selectedSupplier;
  String _paymentMethod = 'نقدي';
  final List<PurchaseItemDisplay> _purchaseItems = [];
  bool _isLoading = false;
  DateTime _selectedDate = DateTime.now();

  // متغيرات للتعديل
  Purchase? _existingPurchase;
  bool _isEditMode = false;
  bool _isLoadingExistingPurchase = false;

  @override
  void initState() {
    super.initState();
    _isEditMode = widget.purchaseId != null;

    // تهيئة حقل المبلغ المدفوع
    _paidAmountController.text = _isEditMode && _existingPurchase != null
        ? _existingPurchase!.totalPaidAmount.toString()
        : '0.0';

    // إضافة مستمع للتنظيف عند التركيز
    _paidAmountFocusNode.addListener(_onPaidAmountFocusChange);

    // إضافة مستمع للخصم لتحديث المبلغ المدفوع
    _discountController.addListener(_updatePaidAmountForCashPayment);

    if (_isEditMode) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _loadExistingPurchase();
      });
    }
  }

  void _onPaidAmountFocusChange() {
    if (_paidAmountFocusNode.hasFocus) {
      // عند التركيز على الحقل، إذا كانت القيمة 0 أو 0.0، امسحها
      final currentValue = _paidAmountController.text.trim();
      if (currentValue == '0' ||
          currentValue == '0.0' ||
          currentValue == '0.00') {
        _paidAmountController.clear();
      }
    }
  }

  void _updatePaidAmountForCashPayment() {
    // تحديث المبلغ المدفوع تلقائياً للدفع النقدي عند تغيير الإجمالي
    if (_paymentMethod == 'نقدي' && _netAmount > 0) {
      _paidAmountController.text = _netAmount.toStringAsFixed(2);
    }
  }

  void _updatePaymentMethodLogic() {
    // تطبيق المنطق الديناميكي حسب طريقة الدفع
    switch (_paymentMethod) {
      case 'نقدي':
        // تعبئة المبلغ المدفوع بالصافي وتعطيل التعديل
        _paidAmountController.text = _netAmount.toStringAsFixed(2);
        break;
      case 'آجل':
        // تصفير المبلغ المدفوع وتعطيل التعديل
        _paidAmountController.text = '0.0';
        break;
      case 'مدفوع جزئياً':
        // السماح بالتعديل - لا نغير القيمة الحالية
        break;
    }
  }

  @override
  void dispose() {
    _invoiceNumberController.dispose();
    _notesController.dispose();
    _discountController.dispose();
    _paidAmountController.dispose();
    _paidAmountFocusNode.removeListener(_onPaidAmountFocusChange);
    _paidAmountFocusNode.dispose();
    _discountController.removeListener(_updatePaidAmountForCashPayment);
    super.dispose();
  }

  // تحميل الفاتورة الموجودة للتعديل
  Future<void> _loadExistingPurchase() async {
    if (widget.purchaseId == null) return;

    setState(() => _isLoadingExistingPurchase = true);

    try {
      final purchaseProvider = context.read<PurchaseProvider>();
      final supplierProvider = context.read<SupplierProvider>();

      // تحميل الفاتورة
      final purchase = await purchaseProvider.getPurchaseById(
        widget.purchaseId!,
      );
      if (purchase == null) {
        throw Exception('لم يتم العثور على الفاتورة');
      }

      // تحميل عناصر الفاتورة
      final purchaseItems = await purchaseProvider.getPurchaseItems(
        widget.purchaseId!,
      );

      // تحميل بيانات المورد
      Supplier? supplier;
      if (purchase.supplierId != null) {
        supplier = await supplierProvider.getSupplierById(purchase.supplierId!);
      }

      // تحويل PurchaseItems إلى PurchaseItemDisplay
      final List<PurchaseItemDisplay> displayItems = [];
      for (final item in purchaseItems) {
        String productName = 'منتج رقم ${item.productId}';

        try {
          final getProductByIdUseCase = GetIt.instance<GetProductByIdUseCase>();
          final product = await getProductByIdUseCase.call(item.productId);
          productName = product?.name ?? 'منتج رقم ${item.productId}';
        } catch (e) {
          // استخدام الاسم الافتراضي في حالة الخطأ
        }

        displayItems.add(
          PurchaseItemDisplay(
            productId: item.productId,
            productName: productName,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            unit: null, // سيتم تحديثها لاحقاً إذا لزم الأمر
          ),
        );
      }

      if (mounted) {
        setState(() {
          _existingPurchase = purchase;
          _selectedSupplier = supplier;
          _selectedDate = purchase.purchaseDate;
          _paymentMethod = purchase.paymentMethod;
          _paidAmountController.text = purchase.totalPaidAmount.toString();
          _discountController.text = purchase.discountAmount.toString();
          _notesController.text = purchase.notes ?? '';
          _purchaseItems.clear();
          _purchaseItems.addAll(displayItems);
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الفاتورة: $e')));
        Navigator.of(context).pop();
      }
    } finally {
      if (mounted) {
        setState(() => _isLoadingExistingPurchase = false);
      }
    }
  }

  double get _totalAmount {
    double total = 0;
    for (var item in _purchaseItems) {
      total += item.totalPrice;
    }
    return total;
  }

  double get _discountAmount {
    return double.tryParse(_discountController.text) ?? 0.0;
  }

  double get _netAmount {
    return _totalAmount - _discountAmount;
  }

  double get _dueAmount {
    final paidAmount = double.tryParse(_paidAmountController.text) ?? 0.0;
    return _netAmount - paidAmount;
  }

  Future<void> _selectDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 30)),
      locale: const Locale('ar'),
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectSupplier() async {
    try {
      final suppliers = await context
          .read<SupplierProvider>()
          .getAllSuppliers();
      if (!mounted) return;

      final selected = await showDialog<Supplier>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('اختيار المورد'),
          content: SizedBox(
            width: double.maxFinite,
            height: 400,
            child: suppliers.isEmpty
                ? const Center(child: Text('لا توجد موردين'))
                : ListView.builder(
                    itemCount: suppliers.length,
                    itemBuilder: (context, index) {
                      final supplier = suppliers[index];
                      return ListTile(
                        title: Text(supplier.name),
                        subtitle: Text(supplier.phone ?? 'لا يوجد هاتف'),
                        onTap: () => Navigator.of(context).pop(supplier),
                      );
                    },
                  ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
          ],
        ),
      );

      if (selected != null) {
        setState(() {
          _selectedSupplier = selected;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل الموردين: $e')));
      }
    }
  }

  void _addPurchaseItem() {
    showDialog(
      context: context,
      builder: (context) => _AddPurchaseItemDialog(
        onItemAdded: (item) {
          setState(() {
            _purchaseItems.add(item);
          });
        },
      ),
    );
  }

  void _editItem(int index) {
    final item = _purchaseItems[index];
    showDialog(
      context: context,
      builder: (context) => _AddPurchaseItemDialog(
        onItemAdded: (editedItem) {
          setState(() {
            _purchaseItems[index] = editedItem;
          });
        },
        initialItem: item,
      ),
    );
  }

  void _removeItem(int index) {
    _confirmDelete(index);
  }

  void _confirmDelete(int index) {
    final item = _purchaseItems[index];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: Text(
            'هل أنت متأكد من حذف "${item.productName}" من الفاتورة؟',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _purchaseItems.removeAt(index);
                  _updatePaidAmountForCashPayment();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('حذف'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _savePurchase() async {
    if (!_formKey.currentState!.validate()) return;

    if (_selectedSupplier == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار المورد')));
      return;
    }

    if (_purchaseItems.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إضافة عناصر للفاتورة')),
      );
      return;
    }

    setState(() => _isLoading = true);
    try {
      final purchaseProvider = context.read<PurchaseProvider>();

      if (_isEditMode && _existingPurchase != null) {
        // تحديث الفاتورة الموجودة
        final updatedPurchase = Purchase(
          id: _existingPurchase!.id,
          supplierId: _selectedSupplier!.id,
          purchaseDate: _selectedDate,
          totalAmount: _totalAmount,
          totalPaidAmount: _paymentMethod == 'نقدي' ? _totalAmount : 0.0,
          paymentMethod: _paymentMethod,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          status: _existingPurchase!.status,
          createdAt: _existingPurchase!.createdAt,
          updatedAt: DateTime.now(),
        );

        // تحديث الفاتورة مع عناصرها
        final purchaseItems = _purchaseItems
            .map(
              (item) => PurchaseItem(
                purchaseId: _existingPurchase!.id!,
                productId: item.productId!,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
              ),
            )
            .toList();

        final success = await purchaseProvider.updatePurchaseWithItems(
          updatedPurchase,
          purchaseItems,
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث فاتورة الشراء بنجاح')),
          );
          context.pop();
        }
      } else {
        // إنشاء فاتورة جديدة
        final purchase = Purchase(
          supplierId: _selectedSupplier!.id,
          purchaseDate: _selectedDate,
          totalAmount: _totalAmount,
          totalPaidAmount: _paymentMethod == 'نقدي' ? _totalAmount : 0.0,
          paymentMethod: _paymentMethod,
          notes: _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
          status: 'completed',
        );

        // Create PurchaseItem entities
        final purchaseItems = _purchaseItems
            .map(
              (item) => PurchaseItem(
                purchaseId: 0, // Will be set by the database
                productId: item.productId!,
                quantity: item.quantity,
                unitPrice: item.unitPrice,
              ),
            )
            .toList();

        final success = await purchaseProvider.createPurchase(
          purchase,
          purchaseItems,
          paidAmount:
              0.0, // افتراضياً غير مدفوعة - سيتم إضافة واجهة الدفع لاحقاً
        );

        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء فاتورة الشراء بنجاح')),
          );
          context.pop();
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في حفظ الفاتورة: $e')));
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // عرض شاشة التحميل إذا كان يتم تحميل الفاتورة الموجودة
    if (_isLoadingExistingPurchase) {
      return const SecondaryScreenWrapper(
        title: 'تحميل الفاتورة...',
        child: Center(child: CircularProgressIndicator()),
      );
    }

    return SecondaryScreenWrapper(
      title: _isEditMode ? 'تعديل فاتورة الشراء' : 'إنشاء فاتورة شراء',
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // التاريخ
              Card(
                child: ListTile(
                  leading: const Icon(Icons.calendar_today),
                  title: const Text('تاريخ الفاتورة'),
                  subtitle: Text(
                    '${_selectedDate.day}/${_selectedDate.month}/${_selectedDate.year}',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectDate,
                ),
              ),
              const SizedBox(height: 16),

              // اختيار المورد
              Card(
                child: ListTile(
                  leading: const Icon(Icons.business),
                  title: const Text('المورد *'),
                  subtitle: Text(
                    _selectedSupplier?.name ?? 'اختر المورد',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: _selectedSupplier != null
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _selectSupplier,
                ),
              ),
              const SizedBox(height: 16),

              // رقم الفاتورة
              TextFormField(
                controller: _invoiceNumberController,
                decoration: const InputDecoration(
                  labelText: 'رقم الفاتورة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.receipt_long),
                ),
              ),
              const SizedBox(height: 16),

              // طريقة الدفع
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'طريقة الدفع',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  SegmentedButton<String>(
                    segments: const [
                      ButtonSegment<String>(
                        value: 'نقدي',
                        label: Text('نقدي'),
                        icon: Icon(Icons.money),
                      ),
                      ButtonSegment<String>(
                        value: 'آجل',
                        label: Text('آجل'),
                        icon: Icon(Icons.schedule),
                      ),
                      ButtonSegment<String>(
                        value: 'مدفوع جزئياً',
                        label: Text('جزئي'),
                        icon: Icon(Icons.payments),
                      ),
                    ],
                    selected: {_paymentMethod},
                    onSelectionChanged: (Set<String> newSelection) {
                      setState(() {
                        _paymentMethod = newSelection.first;
                        _updatePaymentMethodLogic();
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // إضافة عناصر
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'عناصر الفاتورة',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          ElevatedButton.icon(
                            onPressed: _addPurchaseItem,
                            icon: const Icon(Icons.add),
                            label: const Text('إضافة منتج'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (_purchaseItems.isEmpty)
                        const Center(
                          child: Padding(
                            padding: EdgeInsets.all(20),
                            child: Text('لم يتم إضافة أي عناصر بعد'),
                          ),
                        )
                      else
                        ...List.generate(_purchaseItems.length, (index) {
                          final item = _purchaseItems[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 8),
                            child: ListTile(
                              title: Text(
                                item.unit != null
                                    ? '${item.productName} - ${item.unit}'
                                    : item.productName,
                              ),
                              subtitle: Text(
                                'الكمية: ${item.quantity} × ${item.unitPrice.toStringAsFixed(2)} ر.ي',
                              ),
                              trailing: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '${item.totalPrice.toStringAsFixed(2)} ر.ي',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.edit,
                                      color: Colors.blue,
                                    ),
                                    onPressed: () => _editItem(index),
                                  ),
                                  IconButton(
                                    icon: const Icon(
                                      Icons.delete,
                                      color: Colors.red,
                                    ),
                                    onPressed: () => _removeItem(index),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // حقل الخصم
              TextFormField(
                controller: _discountController,
                decoration: const InputDecoration(
                  labelText: 'مبلغ الخصم',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.discount),
                  suffixText: 'ر.ي',
                  helperText: 'اختياري - سيتم خصمه من الإجمالي',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*')),
                ],
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    final discount = double.tryParse(value);
                    if (discount == null || discount < 0) {
                      return 'مبلغ خصم غير صحيح';
                    }
                    if (discount >= _totalAmount) {
                      return 'الخصم لا يمكن أن يكون أكبر من أو يساوي الإجمالي';
                    }
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // إعادة بناء الواجهة لتحديث الحسابات
                  });
                },
              ),
              const SizedBox(height: 16),

              // المبلغ المدفوع
              TextFormField(
                controller: _paidAmountController,
                focusNode: _paidAmountFocusNode,
                enabled:
                    _paymentMethod == 'مدفوع جزئياً', // تفعيل فقط للدفع الجزئي
                decoration: InputDecoration(
                  labelText: 'المبلغ المدفوع',
                  border: const OutlineInputBorder(),
                  prefixIcon: const Icon(Icons.payments),
                  suffixText: 'ر.ي',
                  helperText: _paymentMethod == 'نقدي'
                      ? 'يتم ملؤه تلقائياً للدفع النقدي'
                      : _paymentMethod == 'آجل'
                      ? 'صفر للدفع الآجل'
                      : 'يمكن تعديله للدفع الجزئي',
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d*')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال المبلغ المدفوع';
                  }
                  final paidAmount = double.tryParse(value);
                  if (paidAmount == null || paidAmount < 0) {
                    return 'مبلغ غير صحيح';
                  }
                  if (paidAmount > _netAmount) {
                    return 'المبلغ المدفوع أكبر من الصافي';
                  }
                  return null;
                },
                onChanged: (value) {
                  setState(() {
                    // إعادة بناء الواجهة لتحديث الإجمالي والمبلغ المستحق في الفاتورة
                  });
                },
              ),
              const SizedBox(height: 16),

              // ملاحظات
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
              ),
              const SizedBox(height: 16),

              // ملخص الفاتورة
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'ملخص الفاتورة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 12),

                      // الإجمالي قبل الخصم
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('الإجمالي قبل الخصم:'),
                          Text(
                            '${_totalAmount.toStringAsFixed(2)} ر.ي',
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // الخصم
                      if (_discountAmount > 0) ...[
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text('الخصم:'),
                            Text(
                              '- ${_discountAmount.toStringAsFixed(2)} ر.ي',
                              style: TextStyle(
                                fontWeight: FontWeight.w500,
                                color: Colors.red.shade600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                      ],

                      // الصافي بعد الخصم
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          border: Border(
                            top: BorderSide(color: Colors.grey.shade300),
                            bottom: BorderSide(color: Colors.grey.shade300),
                          ),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            const Text(
                              'الصافي بعد الخصم:',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              '${_netAmount.toStringAsFixed(2)} ر.ي',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 8),

                      // المبلغ المدفوع
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text('المبلغ المدفوع:'),
                          Text(
                            '${(double.tryParse(_paidAmountController.text) ?? 0.0).toStringAsFixed(2)} ر.ي',
                            style: TextStyle(
                              fontWeight: FontWeight.w500,
                              color: Colors.blue.shade600,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),

                      // المبلغ المتبقي
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'المبلغ المتبقي:',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            '${_dueAmount.toStringAsFixed(2)} ر.ي',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: _dueAmount > 0
                                  ? Colors.red.shade600
                                  : Colors.green.shade600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // زر الحفظ
              ElevatedButton(
                onPressed: _isLoading ? null : _savePurchase,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: _isLoading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                    : Text(
                        _isEditMode ? 'تحديث الفاتورة' : 'حفظ الفاتورة',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
              const SizedBox(height: 16),

              // ملاحظة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  border: Border.all(color: Colors.orange.shade200),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.orange.shade700),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'ملاحظة: وحدة المشتريات قيد التطوير. هذا النموذج للعرض فقط.',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _AddPurchaseItemDialog extends StatefulWidget {
  final Function(PurchaseItemDisplay) onItemAdded;
  final PurchaseItemDisplay? initialItem;

  const _AddPurchaseItemDialog({required this.onItemAdded, this.initialItem});

  @override
  State<_AddPurchaseItemDialog> createState() => _AddPurchaseItemDialogState();
}

class _AddPurchaseItemDialogState extends State<_AddPurchaseItemDialog> {
  final _quantityController = TextEditingController();
  final _unitPriceController = TextEditingController();
  Product? _selectedProduct;
  List<Product> _products = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    // If editing, set initial values
    if (widget.initialItem != null) {
      _quantityController.text = widget.initialItem!.quantity.toString();
      _unitPriceController.text = widget.initialItem!.unitPrice.toString();
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadProducts();
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _unitPriceController.dispose();
    super.dispose();
  }

  Future<void> _loadProducts() async {
    try {
      final productProvider = context.read<ProductProvider>();
      await productProvider.fetchProducts();
      if (mounted) {
        final products = productProvider.products;
        setState(() {
          _products = products;
          _isLoading = false;

          // If editing, find and select the initial product
          if (widget.initialItem != null &&
              widget.initialItem!.productId != null) {
            _selectedProduct = products.firstWhere(
              (p) => p.id == widget.initialItem!.productId,
              orElse: () => products.first,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('خطأ في تحميل المنتجات: $e')));
      }
    }
  }

  void _selectProduct(Product product) {
    setState(() {
      _selectedProduct = product;
      _unitPriceController.text =
          product.lastPurchasePrice?.toString() ?? '0.0';
      _quantityController.text = '1'; // Default quantity
    });
  }

  void _addItem() {
    if (_selectedProduct == null) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى اختيار منتج')));
      return;
    }

    if (_quantityController.text.trim().isEmpty ||
        _unitPriceController.text.trim().isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('يرجى ملء جميع الحقول')));
      return;
    }

    final quantity = double.tryParse(_quantityController.text.trim());
    final unitPrice = double.tryParse(_unitPriceController.text.trim());

    if (quantity == null ||
        quantity <= 0 ||
        unitPrice == null ||
        unitPrice <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال قيم صحيحة للكمية والسعر')),
      );
      return;
    }

    final item = PurchaseItemDisplay(
      productId: _selectedProduct!.id,
      productName: _selectedProduct!.name,
      quantity: quantity.toInt(),
      unitPrice: unitPrice,
      unit: _selectedProduct!.unit,
    );

    widget.onItemAdded(item);
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        widget.initialItem != null ? 'تعديل المنتج' : 'إضافة منتج للفاتورة',
      ),
      content: SizedBox(
        width: double.maxFinite,
        height: 400,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Column(
                children: [
                  // اختيار المنتج
                  Expanded(
                    child: ListView.builder(
                      itemCount: _products.length,
                      itemBuilder: (context, index) {
                        final product = _products[index];
                        final isSelected = _selectedProduct?.id == product.id;
                        return Card(
                          color: isSelected ? Colors.orange.shade50 : null,
                          child: ListTile(
                            title: Text(product.name),
                            subtitle: Text(
                              'آخر سعر شراء: ${product.lastPurchasePrice?.toStringAsFixed(2) ?? 'غير محدد'} ر.ي\n'
                              'المخزون الحالي: ${product.totalQuantity}',
                            ),
                            trailing: isSelected
                                ? const Icon(
                                    Icons.check_circle,
                                    color: Colors.orange,
                                  )
                                : null,
                            onTap: () => _selectProduct(product),
                          ),
                        );
                      },
                    ),
                  ),
                  const SizedBox(height: 16),
                  // الكمية والسعر
                  if (_selectedProduct != null) ...[
                    TextField(
                      controller: _quantityController,
                      decoration: const InputDecoration(
                        labelText: 'الكمية',
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _unitPriceController,
                      decoration: const InputDecoration(
                        labelText: 'سعر الوحدة',
                        border: OutlineInputBorder(),
                        suffixText: 'ريال',
                      ),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade50,
                        border: Border.all(color: Colors.orange.shade200),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'الإجمالي: ${((double.tryParse(_unitPriceController.text) ?? 0) * (double.tryParse(_quantityController.text) ?? 0)).toStringAsFixed(2)} ر.ي',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        if (_selectedProduct != null)
          ElevatedButton(
            onPressed: _addItem,
            child: Text(widget.initialItem != null ? 'تحديث' : 'إضافة'),
          ),
      ],
    );
  }
}
