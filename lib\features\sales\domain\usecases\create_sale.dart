import '../entities/sale.dart';
import '../entities/sale_item.dart';
import '../repositories/sale_repository.dart';

class CreateSaleUseCase {
  final SaleRepository _repository;

  CreateSaleUseCase(this._repository);

  Future<int> call(Sale sale, List<SaleItem> items) async {
    // Validation
    if (items.isEmpty) {
      throw Exception('Sale must contain at least one item');
    }

    if (sale.totalAmount < 0) {
      throw Exception('Total amount cannot be negative');
    }

    if (sale.totalPaidAmount < 0) {
      throw Exception('Paid amount cannot be negative');
    }

    if (sale.totalPaidAmount > sale.totalAmount) {
      throw Exception('Paid amount cannot exceed total amount');
    }

    if (sale.paymentMethod.isEmpty) {
      throw Exception('Payment method cannot be empty');
    }

    if (sale.status.isEmpty) {
      throw Exception('Sale status cannot be empty');
    }

    // Validate items
    for (final item in items) {
      if (item.unitPrice < 0) {
        throw Exception('Item unit price cannot be negative');
      }

      if (item.isWholesaleProduct) {
        if (item.productId == null) {
          throw Exception('Product ID is required for wholesale products');
        }
        if (item.quantity == null || item.quantity! <= 0) {
          throw Exception(
            'Quantity must be greater than 0 for wholesale products',
          );
        }
      }

      if (item.isRetailGoodsSummary) {
        if (item.description == null || item.description!.isEmpty) {
          throw Exception('Description is required for retail goods summary');
        }
      }
    }

    // Validate that total matches sum of items
    final calculatedTotal = items.fold<double>(
      0.0,
      (sum, item) => sum + item.totalPrice,
    );

    if ((sale.totalAmount - calculatedTotal).abs() > 0.01) {
      throw Exception('Total amount does not match sum of items');
    }

    // Validate payment amounts - dueAmount is now calculated automatically
    // No need for additional validation since dueAmount = totalAmount - totalPaidAmount

    try {
      return await _repository.createSale(sale, items);
    } catch (e) {
      throw Exception('Failed to create sale: $e');
    }
  }
}
