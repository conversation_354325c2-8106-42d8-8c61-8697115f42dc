import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:get_it/get_it.dart';
import '../../../customers/domain/entities/customer.dart';
import '../../../customers/domain/entities/customer_account.dart';
import '../../../customers/domain/entities/customer_account_statement_item.dart';
import '../../domain/usecases/create_customer.dart';
import '../../domain/usecases/update_customer.dart';
import '../../domain/usecases/get_all_customers.dart';
import '../../domain/usecases/get_customer_by_id.dart';
import '../../domain/usecases/delete_customer.dart';
import '../../../customers/domain/usecases/add_customer_account_entry.dart';
import '../../../customers/domain/usecases/get_customer_account_statement.dart';
import '../../domain/repositories/customer_account_repository.dart';
import '../../../sales/domain/repositories/sale_repository.dart';
import '../../../../utils/excel_importer.dart';

class CustomerProvider extends ChangeNotifier {
  final CreateCustomer _createCustomerUseCase;
  final UpdateCustomer _updateCustomerUseCase;
  final GetAllCustomers _getAllCustomersUseCase;
  final GetCustomerById _getCustomerByIdUseCase;
  final DeleteCustomer _deleteCustomerUseCase;
  final AddCustomerAccountEntryUseCase _addCustomerAccountEntryUseCase;
  final GetCustomerAccountStatementUseCase _getCustomerAccountStatementUseCase;

  CustomerProvider(
    this._createCustomerUseCase,
    this._updateCustomerUseCase,
    this._getAllCustomersUseCase,
    this._getCustomerByIdUseCase,
    this._deleteCustomerUseCase,
    this._addCustomerAccountEntryUseCase,
    this._getCustomerAccountStatementUseCase,
  );

  // State variables
  List<Customer> _customers = [];
  List<CustomerAccountStatementItem> _statementItems = [];
  bool _isLoading = false;
  String? _errorMessage;
  String? _selectedBalanceFilter; // للفلترة حسب حالة الرصيد

  // Getters
  List<Customer> get customers => _customers;
  List<CustomerAccountStatementItem> get statementItems => _statementItems;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get selectedBalanceFilter => _selectedBalanceFilter;

  // Load customers from database
  Future<void> loadCustomers() async {
    _setLoading(true);
    _clearError();

    try {
      _customers = await _getAllCustomersUseCase.call();
      notifyListeners();
    } catch (e) {
      _setError('فشل في تحميل العملاء: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  // Add sale to customer account
  Future<bool> addSaleToAccount(
    int customerId,
    int invoiceId,
    double amount,
    String description,
  ) async {
    try {
      final entry = CustomerAccount(
        customerId: customerId,
        transactionDate: DateTime.now(),
        type: 'sale_invoice',
        amount: amount,
        description: description,
        relatedInvoiceId: invoiceId,
        isPaid: false,
      );

      await _addCustomerAccountEntryUseCase.call(entry);
      return true;
    } catch (e) {
      _setError('فشل في إضافة البيع للحساب: ${e.toString()}');
      return false;
    }
  }

  // Add retail debt to customer account
  Future<bool> addRetailDebtToAccount(
    int customerId,
    double amount,
    String description,
  ) async {
    try {
      final entry = CustomerAccount(
        customerId: customerId,
        transactionDate: DateTime.now(),
        type: 'retail_debt',
        amount: amount,
        description: description,
        isPaid: false,
      );

      await _addCustomerAccountEntryUseCase.call(entry);
      return true;
    } catch (e) {
      _setError('فشل في إضافة دين التجزئة للحساب: ${e.toString()}');
      return false;
    }
  }

  // Add payment to customer account
  Future<bool> addPaymentToAccount(
    int customerId,
    double amount,
    String description,
  ) async {
    try {
      final entry = CustomerAccount(
        customerId: customerId,
        transactionDate: DateTime.now(),
        type: 'payment',
        amount: amount,
        description: description,
        isPaid: true,
      );

      await _addCustomerAccountEntryUseCase.call(entry);
      return true;
    } catch (e) {
      _setError('فشل في إضافة الدفعة للحساب: ${e.toString()}');
      return false;
    }
  }

  // Add general customer account entry
  Future<bool> addCustomerAccountEntry(CustomerAccount entry) async {
    try {
      await _addCustomerAccountEntryUseCase.call(entry);
      return true;
    } catch (e) {
      _setError('فشل في إضافة قيد حساب العميل: ${e.toString()}');
      return false;
    }
  }

  // Get customer account statement
  Future<List<CustomerAccount>> getCustomerAccountStatement(
    int customerId, {
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    try {
      return await _getCustomerAccountStatementUseCase.call(
        customerId,
        fromDate: fromDate,
        toDate: toDate,
      );
    } catch (e) {
      _setError('فشل في الحصول على كشف حساب العميل: ${e.toString()}');
      return [];
    }
  }

  // Get customer balance
  Future<double> getCustomerBalance(int customerId) async {
    try {
      return await _getCustomerAccountStatementUseCase.getCustomerBalance(
        customerId,
      );
    } catch (e) {
      _setError('فشل في الحصول على رصيد العميل: ${e.toString()}');
      return 0.0;
    }
  }

  // Get customer financial summary
  Future<Map<String, dynamic>> getCustomerFinancialSummary(
    int customerId,
  ) async {
    try {
      final saleRepository = GetIt.instance<SaleRepository>();
      final customerAccountRepository =
          GetIt.instance<CustomerAccountRepository>();

      final totalSales = await saleRepository.getTotalSalesAmountByCustomerId(
        customerId,
      );
      final totalPayments = await customerAccountRepository
          .getTotalPaymentsReceivedForCustomer(customerId);
      final currentBalance = await customerAccountRepository.getCustomerBalance(
        customerId,
      );

      return {
        'totalSales': totalSales,
        'totalPayments': totalPayments,
        'currentBalance': currentBalance,
      };
    } catch (e) {
      _setError('فشل في الحصول على الملخص المالي للعميل: ${e.toString()}');
      return {'totalSales': 0.0, 'totalPayments': 0.0, 'currentBalance': 0.0};
    }
  }

  // Fetch account statement with running balance calculation
  Future<void> fetchAccountStatement(int customerId) async {
    _setLoading(true);
    _clearError();

    try {
      // 1. جلب المعاملات مرتبة حسب التاريخ تصاعدياً
      final transactions = await _getCustomerAccountStatementUseCase.call(
        customerId,
        // ملاحظة: تأكد من أن use case والمستودع يجلب البيانات مرتبة بـ ASC
      );

      // 2. حساب الرصيد الجاري
      final List<CustomerAccountStatementItem> items = [];
      double runningBalance = 0.0;

      for (final transaction in transactions) {
        double debit = 0.0;
        double credit = 0.0;

        if (transaction.isDebit) {
          // isDebit من الكيان CustomerAccount
          debit = transaction.amount;
        } else {
          credit = transaction.amount;
        }

        runningBalance += debit - credit;

        items.add(
          CustomerAccountStatementItem(
            transaction: transaction,
            debit: debit,
            credit: credit,
            runningBalance: runningBalance,
          ),
        );
      }

      // 3. عكس القائمة لعرض الأحدث أولاً في الواجهة وتخزينها
      _statementItems = items.reversed.toList();
    } catch (e) {
      _setError('فشل في تحميل كشف الحساب: ${e.toString()}');
    } finally {
      _setLoading(false); // هذا سيقوم بـ notifyListeners()
    }
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
  }

  // Import customers from device contacts
  Future<int> importFromContacts() async {
    _setLoading(true);
    _clearError();

    try {
      // Request permission to access contacts
      if (!await FlutterContacts.requestPermission()) {
        _setError(
          'تم رفض إذن الوصول إلى جهات الاتصال. يرجى تمكينه يدوياً من الإعدادات.',
        );
        await openAppSettings();
        throw Exception('تم رفض إذن الوصول.');
      }

      // Get all contacts with phone numbers
      final contacts = await FlutterContacts.getContacts(
        withProperties: true,
        withPhoto: false,
      );

      int importedCount = 0;

      for (final contact in contacts) {
        if (contact.displayName.isNotEmpty) {
          try {
            final customer = Customer(
              name: contact.displayName.trim(),
              phone: contact.phones.isNotEmpty
                  ? contact.phones.first.number.trim()
                  : null,
              email: contact.emails.isNotEmpty
                  ? contact.emails.first.address.trim()
                  : null,
              address: contact.addresses.isNotEmpty
                  ? contact.addresses.first.address.trim()
                  : null,
              creditLimit: 0.0,
              notes: null,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            );

            await _createCustomerUseCase.call(customer);
            importedCount++;
          } catch (e) {
            // Skip contacts that fail to import
            continue;
          }
        }
      }

      // Refresh the list after import
      await loadCustomers();

      return importedCount;
    } catch (e) {
      _setError('فشل في استيراد العملاء من جهات الاتصال: ${e.toString()}');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Import customers from Excel file
  Future<ImportResult> importCustomers(File file) async {
    _setLoading(true);
    _clearError();

    try {
      final importedCustomers = await ExcelImporter.importCustomers(file);

      int successCount = 0;
      int errorCount = 0;
      List<String> errors = [];

      for (final customer in importedCustomers) {
        try {
          // Use the real use case to save customers to database
          await _createCustomerUseCase.call(customer);
          successCount++;
        } catch (e) {
          errorCount++;
          errors.add('خطأ في العميل ${customer.name}: $e');
        }
      }

      // Refresh the list after import
      await loadCustomers();

      notifyListeners();

      return ImportResult(
        totalCount: importedCustomers.length,
        successCount: successCount,
        errorCount: errorCount,
        errors: errors,
      );
    } catch (e) {
      _setError('فشل في استيراد العملاء: ${e.toString()}');
      return ImportResult(
        totalCount: 0,
        successCount: 0,
        errorCount: 1,
        errors: [e.toString()],
      );
    } finally {
      _setLoading(false);
    }
  }

  // Search customers
  List<Customer> searchCustomers(String query) {
    if (query.isEmpty) return _customers;

    return _customers.where((customer) {
      final name = customer.name.toLowerCase();
      final phone = customer.phone?.toLowerCase() ?? '';
      final email = customer.email?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();

      return name.contains(searchQuery) ||
          phone.contains(searchQuery) ||
          email.contains(searchQuery);
    }).toList();
  }

  // Filter customers by balance status
  void filterCustomersByBalanceStatus(String? status) {
    _selectedBalanceFilter = status;
    notifyListeners();
  }

  // Get filtered customers (by balance status and search)
  List<Customer> getFilteredCustomers(String searchQuery) {
    List<Customer> filteredCustomers = _customers;

    // Apply balance filter - سيتم تطبيق التصفية لاحقاً بعد حساب الأرصدة ديناميكياً
    // TODO: تحديث منطق التصفية ليعتمد على الحساب الديناميكي للرصيد
    // if (_selectedBalanceFilter != null && _selectedBalanceFilter!.isNotEmpty) {
    //   switch (_selectedBalanceFilter) {
    //     case 'مدينون':
    //       filteredCustomers = filteredCustomers
    //           .where((customer) => customer.currentBalance > 0)
    //           .toList();
    //       break;
    //     case 'دائنون':
    //       filteredCustomers = filteredCustomers
    //           .where((customer) => customer.currentBalance < 0)
    //           .toList();
    //       break;
    //     case 'رصيد صفر':
    //       filteredCustomers = filteredCustomers
    //           .where((customer) => customer.currentBalance == 0)
    //           .toList();
    //       break;
    //   }
    // }

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filteredCustomers = filteredCustomers.where((customer) {
        final name = customer.name.toLowerCase();
        final phone = customer.phone?.toLowerCase() ?? '';
        final email = customer.email?.toLowerCase() ?? '';
        final searchQueryLower = searchQuery.toLowerCase();

        return name.contains(searchQueryLower) ||
            phone.contains(searchQueryLower) ||
            email.contains(searchQueryLower);
      }).toList();
    }

    return filteredCustomers;
  }

  // CRUD operations for CustomerFormScreen
  Future<List<Customer>> getAllCustomers() async {
    try {
      return await _getAllCustomersUseCase.call();
    } catch (e) {
      _setError('فشل في جلب العملاء: ${e.toString()}');
      return [];
    }
  }

  Future<Customer?> getCustomerById(int id) async {
    try {
      return await _getCustomerByIdUseCase.call(id);
    } catch (e) {
      _setError('فشل في جلب العميل: ${e.toString()}');
      return null;
    }
  }

  Future<void> addCustomer(Customer customer) async {
    try {
      await _createCustomerUseCase.call(customer);
      await loadCustomers(); // Refresh the list
    } catch (e) {
      _setError('فشل في إضافة العميل: ${e.toString()}');
      rethrow;
    }
  }

  Future<void> updateCustomer(Customer customer) async {
    try {
      await _updateCustomerUseCase.call(customer);
      await loadCustomers(); // Refresh the list
    } catch (e) {
      _setError('فشل في تحديث العميل: ${e.toString()}');
      rethrow;
    }
  }

  Future<void> deleteCustomer(int customerId) async {
    try {
      await _deleteCustomerUseCase.call(customerId);
      await loadCustomers(); // Refresh the list
    } catch (e) {
      _setError('فشل في حذف العميل: ${e.toString()}');
      rethrow;
    }
  }
}

class ImportResult {
  final int totalCount;
  final int successCount;
  final int errorCount;
  final List<String> errors;

  ImportResult({
    required this.totalCount,
    required this.successCount,
    required this.errorCount,
    required this.errors,
  });

  bool get hasErrors => errorCount > 0;
  bool get isSuccess => successCount > 0;

  String get summary {
    if (totalCount == 0) return 'لم يتم العثور على بيانات للاستيراد';
    if (errorCount == 0) {
      return 'تم استيراد جميع البيانات بنجاح ($successCount عنصر)';
    }
    if (successCount == 0) {
      return 'فشل في استيراد جميع البيانات ($errorCount خطأ)';
    }
    return 'تم استيراد $successCount من أصل $totalCount عنصر ($errorCount خطأ)';
  }
}
