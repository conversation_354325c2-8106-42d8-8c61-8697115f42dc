class SaleModel {
  final int? id;
  final int? customerId;
  final DateTime saleDate;
  final double totalAmount;
  final double totalPaidAmount;
  final String paymentMethod;
  final String? notes;
  final String status;

  const SaleModel({
    this.id,
    this.customerId,
    required this.saleDate,
    required this.totalAmount,
    this.totalPaidAmount = 0.0,
    required this.paymentMethod,
    this.notes,
    required this.status,
  });

  // Convert from Map (from database)
  factory SaleModel.fromMap(Map<String, dynamic> map) {
    return SaleModel(
      id: map['id'] as int?,
      customerId: map['customerId'] as int?,
      saleDate: DateTime.parse(map['saleDate'] as String),
      totalAmount: (map['totalAmount'] as num).toDouble(),
      totalPaidAmount: (map['totalPaidAmount'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: map['paymentMethod'] as String,
      notes: map['notes'] as String?,
      status: map['status'] as String,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customerId': customerId,
      'saleDate': saleDate.toIso8601String(),
      'totalAmount': totalAmount,
      'totalPaidAmount': totalPaidAmount,
      'paymentMethod': paymentMethod,
      'notes': notes,
      'status': status,
    };
  }

  // Copy with method for updates
  SaleModel copyWith({
    int? id,
    int? customerId,
    DateTime? saleDate,
    double? totalAmount,
    double? totalPaidAmount,
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return SaleModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      saleDate: saleDate ?? this.saleDate,
      totalAmount: totalAmount ?? this.totalAmount,
      totalPaidAmount: totalPaidAmount ?? this.totalPaidAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'SaleModel(id: $id, customerId: $customerId, saleDate: $saleDate, '
        'totalAmount: $totalAmount, totalPaidAmount: $totalPaidAmount, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SaleModel &&
        other.id == id &&
        other.customerId == customerId &&
        other.saleDate == saleDate &&
        other.totalAmount == totalAmount &&
        other.totalPaidAmount == totalPaidAmount &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        customerId.hashCode ^
        saleDate.hashCode ^
        totalAmount.hashCode ^
        totalPaidAmount.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
