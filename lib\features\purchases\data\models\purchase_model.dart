class PurchaseModel {
  final int? id;
  final int? supplierId;
  final DateTime purchaseDate;
  final double totalAmount;
  final double totalPaidAmount;
  final String paymentMethod;
  final String? notes;
  final String status;

  const PurchaseModel({
    this.id,
    this.supplierId,
    required this.purchaseDate,
    required this.totalAmount,
    this.totalPaidAmount = 0.0,
    required this.paymentMethod,
    this.notes,
    required this.status,
  });

  // Convert from Map (from database)
  factory PurchaseModel.fromMap(Map<String, dynamic> map) {
    return PurchaseModel(
      id: map['id'] as int?,
      supplierId: map['supplierId'] as int?,
      purchaseDate: DateTime.parse(map['purchaseDate'] as String),
      totalAmount: (map['totalAmount'] as num).toDouble(),
      totalPaidAmount: (map['totalPaidAmount'] as num?)?.toDouble() ?? 0.0,
      paymentMethod: map['paymentMethod'] as String,
      notes: map['notes'] as String?,
      status: map['status'] as String,
    );
  }

  // Convert to Map (for database)
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'supplierId': supplierId,
      'purchaseDate': purchaseDate.toIso8601String(),
      'totalAmount': totalAmount,
      'totalPaidAmount': totalPaidAmount,
      'paymentMethod': paymentMethod,
      'notes': notes,
      'status': status,
    };
  }

  // Copy with method for updates
  PurchaseModel copyWith({
    int? id,
    int? supplierId,
    DateTime? purchaseDate,
    double? totalAmount,
    double? totalPaidAmount,
    String? paymentMethod,
    String? notes,
    String? status,
  }) {
    return PurchaseModel(
      id: id ?? this.id,
      supplierId: supplierId ?? this.supplierId,
      purchaseDate: purchaseDate ?? this.purchaseDate,
      totalAmount: totalAmount ?? this.totalAmount,
      totalPaidAmount: totalPaidAmount ?? this.totalPaidAmount,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      notes: notes ?? this.notes,
      status: status ?? this.status,
    );
  }

  @override
  String toString() {
    return 'PurchaseModel(id: $id, supplierId: $supplierId, purchaseDate: $purchaseDate, '
        'totalAmount: $totalAmount, totalPaidAmount: $totalPaidAmount, '
        'paymentMethod: $paymentMethod, notes: $notes, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PurchaseModel &&
        other.id == id &&
        other.supplierId == supplierId &&
        other.purchaseDate == purchaseDate &&
        other.totalAmount == totalAmount &&
        other.totalPaidAmount == totalPaidAmount &&
        other.paymentMethod == paymentMethod &&
        other.notes == notes &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        supplierId.hashCode ^
        purchaseDate.hashCode ^
        totalAmount.hashCode ^
        totalPaidAmount.hashCode ^
        paymentMethod.hashCode ^
        notes.hashCode ^
        status.hashCode;
  }
}
